{"frame": 25017, "sequence": 0, "step": 39, "timestamp": 185.879074, "captures": [{"@type": "type.unity.com/unity.solo.RGBCamera", "id": "camera", "description": "", "position": [14198.8525, -6.55109167, 25.7622566], "rotation": [0.00377062242, 0.8703883, 0.00666639, -0.4923063], "velocity": [0.0, 0.0, 0.0], "acceleration": [0.0, 0.0, 0.0], "filename": "step39.camera.png", "imageFormat": "Png", "dimension": [1920.0, 1080.0], "projection": "Perspective", "matrix": [1.73205054, 0.0, 0.0, 0.0, 3.079201, 0.0, 0.0, 0.0, -1.00000226], "annotations": [{"@type": "type.unity.com/unity.solo.BoundingBox2DAnnotation", "id": "bounding box", "sensorId": "camera", "description": "Produces 2D bounding box annotations for all visible objects that bear a label defined in this labeler's associated label configuration.", "values": [{"instanceId": 1, "labelId": 2, "labelName": "Observation Satellite - Aqua", "origin": [864.0, 292.0], "dimension": [191.0, 603.0]}]}]}]}