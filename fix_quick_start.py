#!/usr/bin/env python3
"""
修复版Aqua卫星检测快速开始脚本

解决YOLOv13n配置文件路径问题
"""

import os
import sys
import time
from pathlib import Path

def main():
    print("🚀 Aqua卫星检测快速开始 (修复版)")
    print("🎯 模型: YOLOv13n")
    print("📊 数据划分: 7:2:1 (训练:验证:测试)")
    print("="*60)
    
    start_time = time.time()
    
    # 1. 检查前置条件
    print("🔍 检查前置条件...")
    
    source_dir = "Aqua/1-RGB-200张"
    if not os.path.exists(source_dir):
        print(f"❌ 源数据目录不存在: {source_dir}")
        return
    
    image_files = list(Path(source_dir).glob("*.camera.png"))
    json_files = list(Path(source_dir).glob("*.frame_data.json"))
    
    print(f"✅ 找到 {len(image_files)} 张图片和 {len(json_files)} 个标注文件")
    
    if len(image_files) == 0 or len(json_files) == 0:
        print("❌ 数据文件不完整")
        return
    
    # 2. 数据转换
    print(f"\n📊 步骤1: 转换数据集 (7:2:1划分)")
    print("-" * 40)
    
    try:
        import convert_aqua_dataset
        config_file = convert_aqua_dataset.create_yolo_dataset(
            source_dir, 
            "datasets/aqua_yolo", 
            train_ratio=0.7, 
            val_ratio=0.2, 
            test_ratio=0.1
        )
        print("✅ 数据集转换完成")
    except Exception as e:
        print(f"❌ 数据转换失败: {e}")
        return
    
    # 3. 模型训练
    print(f"\n🏋️ 步骤2: YOLOv13n模型训练")
    print("-" * 40)
    
    try:
        from ultralytics import YOLO
        
        # 创建YOLOv13n模型 - 使用多种方法尝试
        print("🔧 创建YOLOv13n模型...")
        
        model = None
        
        # 方法1: 使用预训练的YOLOv13n模型
        if os.path.exists('yolov13n.pt'):
            try:
                model = YOLO('yolov13n.pt')
                print("✅ 使用预训练yolov13n.pt创建模型")
            except Exception as e:
                print(f"⚠️  预训练模型加载失败: {e}")
        
        # 方法2: 使用配置文件创建
        if model is None:
            config_files = [
                'yolov13n.yaml',
                'ultralytics/cfg/models/v13/yolov13.yaml'
            ]
            
            for config_file in config_files:
                try:
                    if os.path.exists(config_file):
                        model = YOLO(config_file)
                        print(f"✅ 使用{config_file}创建模型")
                        break
                except Exception as e:
                    print(f"⚠️  {config_file}创建失败: {e}")
                    continue
        
        if model is None:
            print("❌ 无法创建YOLOv13n模型")
            print("💡 建议:")
            print("   1. 确保yolov13n.pt文件存在")
            print("   2. 或确保配置文件路径正确")
            return
        
        # 训练参数 (快速训练)
        train_args = {
            'data': 'datasets/aqua_yolo/aqua_dataset.yaml',
            'epochs': 30,           # 减少轮数用于快速测试
            'batch': 4,             # 小批次避免内存问题
            'imgsz': 640,
            'device': '',           # 自动选择设备
            'project': 'runs/train',
            'name': 'yolov13n_aqua_quick',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'lr0': 0.001,
            'single_cls': True,     # 单类别优化
            'mosaic': 1.0,          # 数据增强
            'mixup': 0.0,
            'copy_paste': 0.0,
            'amp': True,            # 混合精度
            'verbose': True,
            'plots': True,
            'save': True,
            'val': True,
            'patience': 10          # 早停
        }
        
        print("🚀 开始训练...")
        print(f"   - 训练轮数: {train_args['epochs']}")
        print(f"   - 批次大小: {train_args['batch']}")
        print(f"   - 图像尺寸: {train_args['imgsz']}")
        print(f"   - 单类别优化: {train_args['single_cls']}")
        
        # 执行训练
        results = model.train(**train_args)
        print("✅ 训练完成")
        
        # 保存最佳模型
        best_model_path = "runs/train/yolov13n_aqua_quick/weights/best.pt"
        if os.path.exists(best_model_path):
            import shutil
            shutil.copy2(best_model_path, "yolov13n_aqua_quick.pt")
            print("💾 最佳模型已保存: yolov13n_aqua_quick.pt")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 4. 模型测试
    print(f"\n🧪 步骤3: 模型测试")
    print("-" * 40)
    
    try:
        # 加载训练好的模型
        if os.path.exists("yolov13n_aqua_quick.pt"):
            test_model = YOLO("yolov13n_aqua_quick.pt")
            
            # 在测试集上评估
            print("📊 在测试集上评估...")
            test_results = test_model.val(data='datasets/aqua_yolo/aqua_dataset.yaml', split='test')
            
            if hasattr(test_results, 'box') and test_results.box is not None:
                print(f"📈 测试集结果:")
                print(f"   - mAP50-95: {test_results.box.map:.4f}")
                print(f"   - mAP50:    {test_results.box.map50:.4f}")
                print(f"   - mAP75:    {test_results.box.map75:.4f}")
                print(f"   - 精确度:   {test_results.box.mp:.4f}")
                print(f"   - 召回率:   {test_results.box.mr:.4f}")
            
            # 测试几张图片
            test_images_dir = Path("datasets/aqua_yolo/images/test")
            if test_images_dir.exists():
                test_images = list(test_images_dir.glob("*.jpg"))[:3]  # 测试前3张
                
                print(f"\n🖼️  测试图片检测:")
                for i, img_path in enumerate(test_images):
                    results = test_model.predict(source=str(img_path), verbose=False)
                    if results and len(results) > 0:
                        detections = len(results[0].boxes) if results[0].boxes is not None else 0
                        print(f"   - {img_path.name}: 检测到 {detections} 个Aqua卫星")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 5. 总结
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n🎉 快速训练完成!")
    print("="*60)
    print(f"⏱️  总耗时: {total_time/60:.2f} 分钟")
    
    # 检查生成的文件
    print(f"\n📁 生成的文件:")
    if os.path.exists("datasets/aqua_yolo"):
        train_count = len(list(Path("datasets/aqua_yolo/images/train").glob("*.jpg")))
        val_count = len(list(Path("datasets/aqua_yolo/images/val").glob("*.jpg")))
        test_count = len(list(Path("datasets/aqua_yolo/images/test").glob("*.jpg")))
        print(f"✅ 数据集: 训练{train_count}张, 验证{val_count}张, 测试{test_count}张")
    
    if os.path.exists("yolov13n_aqua_quick.pt"):
        print(f"✅ 训练模型: yolov13n_aqua_quick.pt")
    
    if os.path.exists("runs/train/yolov13n_aqua_quick"):
        print(f"✅ 训练结果: runs/train/yolov13n_aqua_quick/")
    
    print(f"\n🎯 使用训练好的模型:")
    print(f"from ultralytics import YOLO")
    print(f"model = YOLO('yolov13n_aqua_quick.pt')")
    print(f"results = model.predict('your_image.jpg')")
    
    print(f"\n✅ Aqua卫星检测快速训练完成!")

if __name__ == '__main__':
    main()
