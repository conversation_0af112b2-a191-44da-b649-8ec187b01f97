#!/usr/bin/env python3
"""
YOLOv13 Aqua卫星模型测试脚本

测试训练后的Aqua卫星检测模型
支持单张图片测试和批量测试
"""

import os
import sys
import argparse
from pathlib import Path
import cv2
import numpy as np

def test_single_image(model, image_path, save_result=True):
    """
    测试单张图片
    
    Args:
        model: YOLO模型
        image_path: 图片路径
        save_result: 是否保存结果
    """
    print(f"🔍 测试图片: {image_path}")
    
    try:
        # 推理
        results = model.predict(source=image_path, verbose=False)
        
        if results and len(results) > 0:
            result = results[0]
            
            # 显示检测结果
            if result.boxes is not None and len(result.boxes) > 0:
                print(f"✅ 检测到 {len(result.boxes)} 个Aqua卫星")
                
                for i, box in enumerate(result.boxes):
                    conf = float(box.conf[0])
                    cls = int(box.cls[0])
                    xyxy = box.xyxy[0].cpu().numpy()
                    
                    print(f"   - 卫星{i+1}: 置信度={conf:.3f}, 位置=[{xyxy[0]:.0f},{xyxy[1]:.0f},{xyxy[2]:.0f},{xyxy[3]:.0f}]")
                
                # 保存结果图片
                if save_result:
                    annotated_img = result.plot()
                    output_path = f"test_result_{Path(image_path).stem}.jpg"
                    cv2.imwrite(output_path, annotated_img)
                    print(f"💾 结果已保存: {output_path}")
                
                return len(result.boxes), results
            else:
                print("❌ 未检测到Aqua卫星")
                return 0, results
        else:
            print("❌ 推理失败")
            return 0, None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 0, None

def test_dataset(model, dataset_dir, max_images=10):
    """
    测试数据集中的图片
    
    Args:
        model: YOLO模型
        dataset_dir: 数据集目录
        max_images: 最大测试图片数
    """
    print(f"📁 测试数据集: {dataset_dir}")
    
    # 查找测试图片
    test_images = []
    for ext in ['*.jpg', '*.png', '*.jpeg']:
        test_images.extend(Path(dataset_dir).glob(f"**/{ext}"))
    
    if not test_images:
        print("❌ 未找到测试图片")
        return
    
    print(f"📸 找到 {len(test_images)} 张图片，测试前 {min(max_images, len(test_images))} 张")
    
    total_detections = 0
    successful_tests = 0
    
    for i, img_path in enumerate(test_images[:max_images]):
        print(f"\n--- 测试 {i+1}/{min(max_images, len(test_images))} ---")
        detections, _ = test_single_image(model, str(img_path), save_result=False)
        
        if detections >= 0:
            successful_tests += 1
            total_detections += detections
    
    print(f"\n📊 测试总结:")
    print(f"   - 成功测试: {successful_tests}/{min(max_images, len(test_images))}")
    print(f"   - 总检测数: {total_detections}")
    print(f"   - 平均每张: {total_detections/successful_tests:.2f}" if successful_tests > 0 else "   - 平均每张: 0")

def main():
    parser = argparse.ArgumentParser(description='YOLOv13n Aqua卫星模型测试')
    parser.add_argument('--model', type=str, default='yolov13n_aqua_best.pt',
                       help='模型文件路径')
    parser.add_argument('--source', type=str, default='datasets/aqua_yolo/images/test',
                       help='测试图片或目录路径 (默认使用测试集)')
    parser.add_argument('--single', action='store_true',
                       help='单张图片测试模式')
    parser.add_argument('--max-images', type=int, default=10,
                       help='最大测试图片数')
    parser.add_argument('--save', action='store_true',
                       help='保存检测结果')
    parser.add_argument('--test-set', action='store_true',
                       help='在测试集上进行完整评估')
    
    args = parser.parse_args()
    
    print("🚀 YOLOv13n Aqua卫星模型测试")
    print("="*50)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        print("可用的模型文件:")
        for model_file in Path('.').glob('*.pt'):
            print(f"   - {model_file}")
        return
    
    try:
        # 加载模型
        from ultralytics import YOLO
        print(f"📦 加载模型: {args.model}")
        model = YOLO(args.model)
        print("✅ 模型加载成功")
        
        # 显示模型信息
        print(f"📊 模型信息:")
        print(f"   - 模型文件: {args.model}")
        print(f"   - 类别数: 1 (Aqua卫星)")

        # 测试集完整评估模式
        if args.test_set:
            print(f"\n🧪 在测试集上进行完整评估...")
            test_config = "datasets/aqua_yolo/aqua_dataset.yaml"
            if os.path.exists(test_config):
                try:
                    # 使用YOLO的val方法在测试集上评估
                    results = model.val(data=test_config, split='test')

                    print(f"\n📊 测试集评估结果:")
                    if hasattr(results, 'box') and results.box is not None:
                        print(f"   - mAP50-95: {results.box.map:.4f}")
                        print(f"   - mAP50:    {results.box.map50:.4f}")
                        print(f"   - mAP75:    {results.box.map75:.4f}")
                        print(f"   - 精确度:   {results.box.mp:.4f}")
                        print(f"   - 召回率:   {results.box.mr:.4f}")
                        print(f"   - F1分数:   {results.box.f1:.4f}")
                    else:
                        print("⚠️  无法获取详细评估指标")
                except Exception as e:
                    print(f"❌ 测试集评估失败: {e}")
            else:
                print(f"❌ 数据集配置文件不存在: {test_config}")

        # 测试模式
        elif args.single or os.path.isfile(args.source):
            # 单张图片测试
            if not os.path.exists(args.source):
                print(f"❌ 图片文件不存在: {args.source}")
                return

            detections, results = test_single_image(model, args.source, args.save)

            if results:
                print(f"\n🎯 测试完成!")
                if detections > 0:
                    print(f"✅ 成功检测到 {detections} 个Aqua卫星")
                else:
                    print("⚠️  未检测到Aqua卫星")
        else:
            # 批量测试
            if not os.path.exists(args.source):
                print(f"❌ 测试目录不存在: {args.source}")
                return

            test_dataset(model, args.source, args.max_images)
        
        print(f"\n✅ 测试完成!")
        
    except ImportError as e:
        print(f"❌ ultralytics导入失败: {e}")
        print("请确保已安装ultralytics")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
