{"frame": 50093, "sequence": 0, "step": 98, "timestamp": 369.095917, "captures": [{"@type": "type.unity.com/unity.solo.RGBCamera", "id": "camera", "description": "", "position": [14195.9912, -1.401298e-45, -0.821806], "rotation": [0.0410376228, -0.6986495, 0.0402029827, 0.713153958], "velocity": [0.0, 0.0, 0.0], "acceleration": [0.0, 0.0, 0.0], "filename": "step98.camera.png", "imageFormat": "Png", "dimension": [1920.0, 1080.0], "projection": "Perspective", "matrix": [1.73205054, 0.0, 0.0, 0.0, 3.079201, 0.0, 0.0, 0.0, -1.00000226], "annotations": [{"@type": "type.unity.com/unity.solo.BoundingBox2DAnnotation", "id": "bounding box", "sensorId": "camera", "description": "Produces 2D bounding box annotations for all visible objects that bear a label defined in this labeler's associated label configuration.", "values": [{"instanceId": 1, "labelId": 2, "labelName": "Observation Satellite - Aqua", "origin": [840.0, 287.0], "dimension": [201.0, 749.0]}]}]}]}