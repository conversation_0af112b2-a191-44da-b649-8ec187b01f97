#!/usr/bin/env python3
"""
Aqua卫星检测完整训练流程

自动执行数据转换、模型训练、测试的完整流程
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 执行成功")
            if result.stdout:
                print("输出:")
                print(result.stdout)
        else:
            print("❌ 执行失败")
            if result.stderr:
                print("错误:")
                print(result.stderr)
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查源数据目录
    source_dir = "Aqua/1-RGB-200张"
    if not os.path.exists(source_dir):
        print(f"❌ 源数据目录不存在: {source_dir}")
        return False
    
    # 检查图片数量
    image_files = list(Path(source_dir).glob("*.camera.png"))
    json_files = list(Path(source_dir).glob("*.frame_data.json"))
    
    print(f"📊 数据统计:")
    print(f"   - 图片文件: {len(image_files)} 个")
    print(f"   - 标注文件: {len(json_files)} 个")
    
    if len(image_files) == 0:
        print("❌ 未找到图片文件")
        return False
    
    if len(json_files) == 0:
        print("❌ 未找到标注文件")
        return False
    
    # 检查YOLOv13配置文件
    config_files = [
        "ultralytics/cfg/models/v13/yolov13n.yaml",
        "ultralytics/cfg/models/v13/yolov13s.yaml"
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            print(f"❌ YOLOv13配置文件不存在: {config_file}")
            return False
    
    print("✅ 前置条件检查通过")
    return True

def main():
    print("🚀 Aqua卫星检测完整训练流程 (YOLOv13n + 7:2:1数据划分)")
    print("=" * 70)
    
    start_time = time.time()
    
    # 1. 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败，训练终止")
        return
    
    # 2. 数据转换
    print(f"\n{'='*60}")
    print("📊 步骤1: 数据集转换")
    print("="*60)
    
    if not run_command("python convert_aqua_dataset.py", "转换Unity数据集为YOLO格式"):
        print("❌ 数据转换失败")
        return
    
    # 检查转换结果
    dataset_config = "datasets/aqua_yolo/aqua_dataset.yaml"
    if not os.path.exists(dataset_config):
        print(f"❌ 数据集配置文件未生成: {dataset_config}")
        return
    
    # 3. 模型训练 - YOLOv13n
    print(f"\n{'='*70}")
    print("🏋️ 步骤2: YOLOv13n模型训练")
    print("="*70)

    train_command = "python train_aqua_yolov13.py --epochs 50 --batch 8 --imgsz 640"
    if not run_command(train_command, "训练YOLOv13n模型"):
        print("❌ YOLOv13n训练失败")
        return
    
    # 4. 模型测试
    print(f"\n{'='*70}")
    print("🧪 步骤3: 模型测试")
    print("="*70)

    # 查找训练好的模型
    model_files = [
        "yolov13n_aqua_best.pt",
        "runs/train/yolov13n_aqua/weights/best.pt"
    ]

    test_model = None
    for model_file in model_files:
        if os.path.exists(model_file):
            test_model = model_file
            break

    if test_model:
        # 在验证集上测试
        test_command = f"python test_aqua_model.py --model {test_model} --source datasets/aqua_yolo/images/val --max-images 5"
        run_command(test_command, f"在验证集上测试模型")

        # 在测试集上完整评估
        eval_command = f"python test_aqua_model.py --model {test_model} --test-set"
        run_command(eval_command, f"在测试集上完整评估模型")
    else:
        print("⚠️  未找到训练好的模型，跳过测试")
    
    # 5. 训练总结
    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n{'='*70}")
    print("📈 YOLOv13n训练流程总结")
    print("="*70)
    print(f"⏱️  总耗时: {total_time/60:.2f} 分钟")
    print(f"📊 数据划分: 7:2:1 (训练:验证:测试)")
    
    # 检查生成的文件
    print(f"\n📁 生成的文件:")
    
    # 数据集文件
    if os.path.exists("datasets/aqua_yolo"):
        print(f"✅ 数据集: datasets/aqua_yolo/")
        train_images = len(list(Path("datasets/aqua_yolo/images/train").glob("*.jpg")))
        val_images = len(list(Path("datasets/aqua_yolo/images/val").glob("*.jpg")))
        test_images = len(list(Path("datasets/aqua_yolo/images/test").glob("*.jpg")))
        print(f"   - 训练图片: {train_images} 张 (70%)")
        print(f"   - 验证图片: {val_images} 张 (20%)")
        print(f"   - 测试图片: {test_images} 张 (10%)")
    
    # 训练结果
    if os.path.exists("runs/train"):
        train_dirs = list(Path("runs/train").glob("yolov13*_aqua*"))
        for train_dir in train_dirs:
            print(f"✅ 训练结果: {train_dir}/")
            
            # 检查权重文件
            best_weights = train_dir / "weights" / "best.pt"
            last_weights = train_dir / "weights" / "last.pt"
            
            if best_weights.exists():
                print(f"   - 最佳权重: {best_weights}")
            if last_weights.exists():
                print(f"   - 最终权重: {last_weights}")
    
    # 测试结果
    test_results = list(Path(".").glob("test_result_*.jpg"))
    if test_results:
        print(f"✅ 测试结果图片: {len(test_results)} 张")
        for result_img in test_results[:3]:  # 只显示前3张
            print(f"   - {result_img}")
    
    print(f"\n🎯 下一步建议:")
    print(f"1. 查看训练图表: runs/train/yolov13n_aqua/")
    print(f"2. 在测试集上评估: python test_aqua_model.py --model yolov13n_aqua_best.pt --test-set")
    print(f"3. 调整训练参数重新训练 (增加epochs或调整数据增强)")
    print(f"4. 导出模型用于部署: model.export(format='onnx')")
    print(f"5. 使用更多Aqua数据扩充数据集")

    print(f"\n✅ YOLOv13n完整训练流程结束!")

if __name__ == '__main__':
    main()
