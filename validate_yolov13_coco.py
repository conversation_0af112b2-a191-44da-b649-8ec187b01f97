#!/usr/bin/env python3
"""
YOLOv13 COCO数据集验证脚本

按照官方README说明验证YOLOv13模型在COCO数据集上的性能
支持验证不同规模的模型：YOLOv13n, YOLOv13s, YOLOv13l, YOLOv13x

使用方法:
    python validate_yolov13_coco.py --model yolov13n.pt
    python validate_yolov13_coco.py --model yolov13s.pt --device 0
    python validate_yolov13_coco.py --model yolov13l.pt --batch 16
"""

import argparse
import os
import sys
import time
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='YOLOv13 COCO验证脚本')
    parser.add_argument('--model', type=str, default='yolov13n.pt', 
                       help='模型文件路径 (yolov13n.pt, yolov13s.pt, yolov13l.pt, yolov13x.pt)')
    parser.add_argument('--data', type=str, default='coco.yaml', 
                       help='数据集配置文件')
    parser.add_argument('--imgsz', type=int, default=640, 
                       help='输入图像尺寸')
    parser.add_argument('--batch', type=int, default=32, 
                       help='批次大小')
    parser.add_argument('--device', type=str, default='', 
                       help='设备 (cpu, 0, 0,1,2,3)')
    parser.add_argument('--half', action='store_true', 
                       help='使用半精度推理')
    parser.add_argument('--save-json', action='store_true', 
                       help='保存COCO格式的结果文件')
    parser.add_argument('--save-hybrid', action='store_true', 
                       help='保存混合标签')
    parser.add_argument('--conf-thres', type=float, default=0.001, 
                       help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.7, 
                       help='NMS IoU阈值')
    parser.add_argument('--max-det', type=int, default=300, 
                       help='每张图像最大检测数量')
    parser.add_argument('--plots', action='store_true', 
                       help='生成验证图表')
    parser.add_argument('--verbose', action='store_true', 
                       help='详细输出')
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("🚀 YOLOv13 COCO数据集验证")
    print("=" * 80)
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"❌ 错误: 模型文件 '{args.model}' 不存在!")
        print("请确保模型文件在当前目录中，或提供正确的路径。")
        print("可用的模型:")
        print("  - yolov13n.pt (Nano - 最快)")
        print("  - yolov13s.pt (Small - 平衡)")
        print("  - yolov13l.pt (Large - 高精度)")
        print("  - yolov13x.pt (Extra Large - 最高精度)")
        return
    
    try:
        # 导入ultralytics
        print("📦 导入ultralytics库...")
        from ultralytics import YOLO
        print("✅ 成功导入ultralytics")
        
        # 加载模型
        print(f"🔄 加载模型: {args.model}")
        model = YOLO(args.model)
        print(f"✅ 成功加载模型: {args.model}")
        
        # 显示模型信息
        print(f"📊 模型信息:")
        print(f"   - 模型类型: {Path(args.model).stem}")
        print(f"   - 输入尺寸: {args.imgsz}x{args.imgsz}")
        print(f"   - 批次大小: {args.batch}")
        print(f"   - 设备: {args.device if args.device else 'auto'}")
        print(f"   - 半精度: {'是' if args.half else '否'}")
        
        # 开始验证
        print("\n🔍 开始COCO数据集验证...")
        print("注意: 首次运行时会自动下载COCO数据集(约20GB)，请耐心等待...")
        
        start_time = time.time()
        
        # 执行验证
        results = model.val(
            data=args.data,
            imgsz=args.imgsz,
            batch=args.batch,
            conf=args.conf_thres,
            iou=args.iou_thres,
            max_det=args.max_det,
            half=args.half,
            device=args.device,
            plots=args.plots,
            save_json=args.save_json,
            save_hybrid=args.save_hybrid,
            verbose=args.verbose
        )
        
        end_time = time.time()
        validation_time = end_time - start_time
        
        # 显示结果
        print("\n" + "=" * 80)
        print("📈 验证结果")
        print("=" * 80)
        
        print(f"⏱️  验证时间: {validation_time:.2f} 秒")
        print(f"🎯 主要指标:")
        print(f"   - mAP50-95: {results.box.map:.4f}")
        print(f"   - mAP50:    {results.box.map50:.4f}")
        print(f"   - mAP75:    {results.box.map75:.4f}")
        print(f"   - 精确度:   {results.box.mp:.4f}")
        print(f"   - 召回率:   {results.box.mr:.4f}")
        print(f"   - F1分数:   {results.box.f1:.4f}")
        
        # 与官方基准对比
        print(f"\n📊 与官方基准对比:")
        model_name = Path(args.model).stem.upper()
        
        # 官方基准数据
        official_benchmarks = {
            'YOLOV13N': {'map': 0.416, 'map50': 0.578, 'map75': 0.451, 'params': '2.5M', 'flops': '6.4G'},
            'YOLOV13S': {'map': 0.480, 'map50': 0.652, 'map75': 0.520, 'params': '9.0M', 'flops': '20.8G'},
            'YOLOV13L': {'map': 0.534, 'map50': 0.709, 'map75': 0.581, 'params': '27.6M', 'flops': '88.4G'},
            'YOLOV13X': {'map': 0.548, 'map50': 0.720, 'map75': 0.598, 'params': '64.0M', 'flops': '199.2G'}
        }
        
        if model_name in official_benchmarks:
            official = official_benchmarks[model_name]
            print(f"   模型: {model_name}")
            print(f"   官方 mAP50-95: {official['map']:.3f} | 实测: {results.box.map:.3f} | 差异: {results.box.map - official['map']:+.3f}")
            print(f"   官方 mAP50:    {official['map50']:.3f} | 实测: {results.box.map50:.3f} | 差异: {results.box.map50 - official['map50']:+.3f}")
            print(f"   官方 mAP75:    {official['map75']:.3f} | 实测: {results.box.map75:.3f} | 差异: {results.box.map75 - official['map75']:+.3f}")
            print(f"   参数量: {official['params']}, FLOPs: {official['flops']}")
        
        # 保存结果
        if args.save_json:
            print(f"\n💾 结果已保存为COCO格式JSON文件")
        
        if args.plots:
            print(f"📊 验证图表已生成")
        
        print(f"\n✅ 验证完成!")
        print("=" * 80)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已正确安装ultralytics:")
        print("pip install -r requirements.txt")
        print("pip install -e .")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        print("请检查:")
        print("1. 模型文件是否正确")
        print("2. 网络连接是否正常(用于下载COCO数据集)")
        print("3. 磁盘空间是否充足(COCO数据集约20GB)")
        print("4. GPU内存是否足够(如使用GPU)")

if __name__ == '__main__':
    main()
