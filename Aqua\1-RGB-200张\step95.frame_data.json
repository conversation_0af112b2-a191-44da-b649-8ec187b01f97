{"frame": 48915, "sequence": 0, "step": 95, "timestamp": 362.3621, "captures": [{"@type": "type.unity.com/unity.solo.RGBCamera", "id": "camera", "description": "", "position": [14168.1221, -46.1037331, 27.4420662], "rotation": [0.09844197, 0.8600926, 0.4664822, -0.181505769], "velocity": [0.0, 0.0, 0.0], "acceleration": [0.0, 0.0, 0.0], "filename": "step95.camera.png", "imageFormat": "Png", "dimension": [1920.0, 1080.0], "projection": "Perspective", "matrix": [1.73205054, 0.0, 0.0, 0.0, 3.079201, 0.0, 0.0, 0.0, -1.00000226], "annotations": [{"@type": "type.unity.com/unity.solo.BoundingBox2DAnnotation", "id": "bounding box", "sensorId": "camera", "description": "Produces 2D bounding box annotations for all visible objects that bear a label defined in this labeler's associated label configuration.", "values": [{"instanceId": 1, "labelId": 2, "labelName": "Observation Satellite - Aqua", "origin": [902.0, 461.0], "dimension": [501.0, 290.0]}]}]}]}