#!/usr/bin/env python3
"""
Aqua数据集转换脚本

将Unity格式的Aqua卫星数据集转换为YOLO格式
支持从JSON标注文件转换为YOLO txt格式
"""

import os
import json
import shutil
from pathlib import Path
import random

def convert_unity_to_yolo(json_file, img_width=1920, img_height=1080):
    """
    将Unity JSON格式转换为YOLO格式
    
    Args:
        json_file: JSON标注文件路径
        img_width: 图像宽度
        img_height: 图像高度
    
    Returns:
        YOLO格式的标注字符串列表
    """
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    yolo_annotations = []
    
    # 遍历所有captures
    for capture in data.get('captures', []):
        # 遍历所有annotations
        for annotation in capture.get('annotations', []):
            if annotation.get('@type') == 'type.unity.com/unity.solo.BoundingBox2DAnnotation':
                # 遍历所有bounding boxes
                for bbox in annotation.get('values', []):
                    label_name = bbox.get('labelName', '')
                    origin = bbox.get('origin', [0, 0])  # [x, y] 左上角
                    dimension = bbox.get('dimension', [0, 0])  # [width, height]
                    
                    # Unity坐标转换为YOLO格式
                    x_center = (origin[0] + dimension[0] / 2) / img_width
                    y_center = (origin[1] + dimension[1] / 2) / img_height
                    width = dimension[0] / img_width
                    height = dimension[1] / img_height
                    
                    # 类别映射 (Aqua卫星 = 类别0)
                    class_id = 0  # 只有一个类别：Aqua卫星
                    
                    # YOLO格式: class_id x_center y_center width height
                    yolo_line = f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}"
                    yolo_annotations.append(yolo_line)
    
    return yolo_annotations

def create_yolo_dataset(source_dir, output_dir, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
    """
    创建YOLO格式的数据集

    Args:
        source_dir: 源数据目录
        output_dir: 输出目录
        train_ratio: 训练集比例 (默认0.7)
        val_ratio: 验证集比例 (默认0.2)
        test_ratio: 测试集比例 (默认0.1)
    """
    source_path = Path(source_dir)
    output_path = Path(output_dir)

    # 创建输出目录结构 (包含测试集)
    train_images_dir = output_path / 'images' / 'train'
    val_images_dir = output_path / 'images' / 'val'
    test_images_dir = output_path / 'images' / 'test'
    train_labels_dir = output_path / 'labels' / 'train'
    val_labels_dir = output_path / 'labels' / 'val'
    test_labels_dir = output_path / 'labels' / 'test'

    for dir_path in [train_images_dir, val_images_dir, test_images_dir,
                     train_labels_dir, val_labels_dir, test_labels_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)

    # 获取所有图像文件
    image_files = list(source_path.glob('*.camera.png'))
    print(f"找到 {len(image_files)} 张图像")

    # 随机打乱并按7:2:1分割数据集
    random.shuffle(image_files)
    total_count = len(image_files)
    train_count = int(total_count * train_ratio)
    val_count = int(total_count * val_ratio)
    test_count = total_count - train_count - val_count  # 剩余的作为测试集

    train_files = image_files[:train_count]
    val_files = image_files[train_count:train_count + val_count]
    test_files = image_files[train_count + val_count:]

    print(f"数据集划分 (7:2:1):")
    print(f"  训练集: {len(train_files)} 张 ({len(train_files)/total_count*100:.1f}%)")
    print(f"  验证集: {len(val_files)} 张 ({len(val_files)/total_count*100:.1f}%)")
    print(f"  测试集: {len(test_files)} 张 ({len(test_files)/total_count*100:.1f}%)")
    
    # 处理训练集
    print(f"\n📁 处理训练集...")
    for img_file in train_files:
        # 复制图像
        new_img_name = img_file.name.replace('.camera.png', '.jpg')
        shutil.copy2(img_file, train_images_dir / new_img_name)

        # 转换标注
        json_file = img_file.with_suffix('.frame_data.json')
        if json_file.exists():
            yolo_annotations = convert_unity_to_yolo(json_file)

            # 保存YOLO标注
            txt_file = train_labels_dir / new_img_name.replace('.jpg', '.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))

    # 处理验证集
    print(f"📁 处理验证集...")
    for img_file in val_files:
        # 复制图像
        new_img_name = img_file.name.replace('.camera.png', '.jpg')
        shutil.copy2(img_file, val_images_dir / new_img_name)

        # 转换标注
        json_file = img_file.with_suffix('.frame_data.json')
        if json_file.exists():
            yolo_annotations = convert_unity_to_yolo(json_file)

            # 保存YOLO标注
            txt_file = val_labels_dir / new_img_name.replace('.jpg', '.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))

    # 处理测试集
    print(f"📁 处理测试集...")
    for img_file in test_files:
        # 复制图像
        new_img_name = img_file.name.replace('.camera.png', '.jpg')
        shutil.copy2(img_file, test_images_dir / new_img_name)

        # 转换标注
        json_file = img_file.with_suffix('.frame_data.json')
        if json_file.exists():
            yolo_annotations = convert_unity_to_yolo(json_file)

            # 保存YOLO标注
            txt_file = test_labels_dir / new_img_name.replace('.jpg', '.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
    
    # 创建数据集配置文件 (包含测试集)
    dataset_config = {
        'path': str(output_path.absolute()),
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': 1,
        'names': ['Aqua_Satellite']
    }

    config_file = output_path / 'aqua_dataset.yaml'
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(f"# Aqua卫星数据集配置 (7:2:1划分)\n")
        f.write(f"path: {dataset_config['path']}\n")
        f.write(f"train: {dataset_config['train']}\n")
        f.write(f"val: {dataset_config['val']}\n")
        f.write(f"test: {dataset_config['test']}\n")
        f.write(f"\n")
        f.write(f"# 类别数量\n")
        f.write(f"nc: {dataset_config['nc']}\n")
        f.write(f"\n")
        f.write(f"# 类别名称\n")
        f.write(f"names:\n")
        for i, name in enumerate(dataset_config['names']):
            f.write(f"  {i}: {name}\n")

    print(f"\n✅ 数据集转换完成!")
    print(f"📁 输出目录: {output_path}")
    print(f"📄 配置文件: {config_file}")
    print(f"📊 数据集统计 (7:2:1划分):")
    print(f"   - 总图像数: {len(image_files)}")
    print(f"   - 训练集: {len(train_files)} 张 ({len(train_files)/len(image_files)*100:.1f}%)")
    print(f"   - 验证集: {len(val_files)} 张 ({len(val_files)/len(image_files)*100:.1f}%)")
    print(f"   - 测试集: {len(test_files)} 张 ({len(test_files)/len(image_files)*100:.1f}%)")
    print(f"   - 类别数: 1 (Aqua卫星)")
    
    return config_file

def main():
    print("🚀 Aqua数据集转换工具")
    print("="*50)
    
    # 设置路径
    source_dir = "Aqua/1-RGB-200张"
    output_dir = "datasets/aqua_yolo"
    
    # 检查源目录
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return
    
    print(f"📂 源目录: {source_dir}")
    print(f"📂 输出目录: {output_dir}")
    
    # 转换数据集 (7:2:1划分)
    config_file = create_yolo_dataset(source_dir, output_dir, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1)
    
    print(f"\n🎯 下一步:")
    print(f"1. 检查转换后的数据集: {output_dir}")
    print(f"2. 使用配置文件训练: {config_file}")
    print(f"3. 运行训练命令:")
    print(f"   python train_aqua_yolov13.py")

if __name__ == '__main__':
    # 设置随机种子确保可重复性
    random.seed(42)
    main()
