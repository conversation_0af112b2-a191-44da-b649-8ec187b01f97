#!/usr/bin/env python3
"""
YOLOv13 简单验证脚本 - 按照README官方示例

直接按照README中的代码示例验证YOLOv13模型
"""

import os
import sys

def validate_model(model_path):
    """验证指定的模型"""
    print(f"🔍 验证模型: {model_path}")
    
    try:
        # 按照README中的示例代码
        from ultralytics import YOLO
        
        # 加载模型 - 按照官方示例
        print(f"📦 加载模型: {model_path}")
        model = YOLO(model_path)
        print(f"✅ 模型加载成功")
        
        # 验证模型性能 - 按照官方示例
        print("🚀 开始验证...")
        print("注意: 首次运行会自动下载COCO数据集，请耐心等待...")
        
        # 使用官方示例的验证代码
        metrics = model.val('coco.yaml')
        
        # 显示结果
        print("\n" + "="*60)
        print("📊 验证结果")
        print("="*60)
        print(f"模型: {model_path}")
        print(f"mAP50-95: {metrics.box.map:.4f}")
        print(f"mAP50:    {metrics.box.map50:.4f}")
        print(f"mAP75:    {metrics.box.map75:.4f}")
        print("="*60)
        
        return metrics
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None

def main():
    print("🚀 YOLOv13 COCO验证 - 官方示例")
    print("="*60)
    
    # 检查可用的模型文件
    available_models = []
    model_files = ['yolov13n.pt', 'yolov13s.pt', 'yolov13l.pt', 'yolov13x.pt']
    
    for model_file in model_files:
        if os.path.exists(model_file):
            available_models.append(model_file)
    
    if not available_models:
        print("❌ 未找到YOLOv13模型文件!")
        print("请确保以下模型文件在当前目录:")
        for model in model_files:
            print(f"  - {model}")
        return
    
    print(f"📁 找到 {len(available_models)} 个模型文件:")
    for model in available_models:
        print(f"  ✅ {model}")
    
    # 验证每个可用的模型
    results = {}
    for model_path in available_models:
        print(f"\n{'='*60}")
        result = validate_model(model_path)
        if result:
            results[model_path] = result
    
    # 汇总结果
    if results:
        print(f"\n{'='*60}")
        print("📈 所有模型验证结果汇总")
        print("="*60)
        print(f"{'模型':<12} {'mAP50-95':<10} {'mAP50':<10} {'mAP75':<10}")
        print("-"*60)
        
        for model_path, metrics in results.items():
            model_name = os.path.basename(model_path)
            print(f"{model_name:<12} {metrics.box.map:<10.4f} {metrics.box.map50:<10.4f} {metrics.box.map75:<10.4f}")
        
        print("="*60)
        print("✅ 验证完成!")
    else:
        print("❌ 所有模型验证都失败了")

if __name__ == '__main__':
    main()
