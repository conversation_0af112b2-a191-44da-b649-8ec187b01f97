#!/usr/bin/env python3
"""
YOLOv13 调试验证脚本

用于调试和解决验证过程中的问题
"""

import os
import sys
import traceback

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 测试基本功能...")
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics导入成功")
        
        # 测试模型加载
        if os.path.exists('yolov13n.pt'):
            print("📦 测试模型加载...")
            model = YOLO('yolov13n.pt')
            print("✅ 模型加载成功")
            
            # 检查模型属性
            print(f"📊 模型信息:")
            print(f"   - 模型类型: {type(model)}")
            print(f"   - 模型名称: {model.model_name if hasattr(model, 'model_name') else 'Unknown'}")
            
            # 测试简单推理
            print("🔍 测试简单推理...")
            try:
                # 使用内置的示例图片
                results = model.predict(source='ultralytics/assets/bus.jpg', verbose=False)
                print("✅ 推理测试成功")
                print(f"   - 检测到 {len(results[0].boxes) if results[0].boxes is not None else 0} 个目标")
            except Exception as e:
                print(f"❌ 推理测试失败: {e}")
            
            return model
        else:
            print("❌ 未找到yolov13n.pt模型文件")
            return None
            
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return None

def test_validation_methods(model):
    """测试不同的验证方法"""
    print("\n🧪 测试验证方法...")
    
    # 方法1: 直接使用data参数
    print("方法1: model.val(data='coco.yaml')")
    try:
        metrics = model.val(data='coco.yaml')
        print("✅ 方法1成功")
        return metrics
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2: 使用完整路径
    print("方法2: model.val(data='ultralytics/cfg/datasets/coco.yaml')")
    try:
        metrics = model.val(data='ultralytics/cfg/datasets/coco.yaml')
        print("✅ 方法2成功")
        return metrics
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
    
    # 方法3: 使用更多参数
    print("方法3: 带更多参数的验证")
    try:
        metrics = model.val(
            data='coco.yaml',
            imgsz=640,
            batch=1,  # 使用小批次
            conf=0.001,
            iou=0.7,
            verbose=True
        )
        print("✅ 方法3成功")
        return metrics
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
    
    # 方法4: 使用coco8作为测试
    print("方法4: 使用coco8测试数据集")
    try:
        metrics = model.val(data='coco8.yaml')
        print("✅ 方法4成功")
        return metrics
    except Exception as e:
        print(f"❌ 方法4失败: {e}")
    
    return None

def check_dataset_config():
    """检查数据集配置"""
    print("\n📁 检查数据集配置...")
    
    config_files = [
        'coco.yaml',
        'ultralytics/cfg/datasets/coco.yaml',
        'ultralytics/cfg/datasets/coco8.yaml'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ 找到配置文件: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'path:' in content:
                        print(f"   - 包含数据路径配置")
                    if 'names:' in content:
                        print(f"   - 包含类别名称配置")
            except Exception as e:
                print(f"   - 读取失败: {e}")
        else:
            print(f"❌ 未找到配置文件: {config_file}")

def main():
    print("🚀 YOLOv13 调试验证")
    print("="*60)
    
    # 检查数据集配置
    check_dataset_config()
    
    # 测试基本功能
    model = test_basic_functionality()
    
    if model is None:
        print("❌ 基本功能测试失败，无法继续")
        return
    
    # 测试验证方法
    metrics = test_validation_methods(model)
    
    if metrics:
        print("\n📊 验证成功! 结果:")
        print("="*60)
        try:
            print(f"mAP50-95: {metrics.box.map:.4f}")
            print(f"mAP50:    {metrics.box.map50:.4f}")
            print(f"mAP75:    {metrics.box.map75:.4f}")
        except Exception as e:
            print(f"结果显示失败: {e}")
            print(f"原始结果: {metrics}")
    else:
        print("\n❌ 所有验证方法都失败了")
        print("\n🔧 可能的解决方案:")
        print("1. 检查网络连接，确保可以下载COCO数据集")
        print("2. 确保有足够的磁盘空间(约20GB)")
        print("3. 尝试手动下载COCO数据集")
        print("4. 检查CUDA/GPU配置")
        print("5. 尝试使用CPU模式: model.val(data='coco.yaml', device='cpu')")

if __name__ == '__main__':
    main()
