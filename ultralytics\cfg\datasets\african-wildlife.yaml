# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# African-wildlife dataset by Ultralytics
# Documentation: https://docs.ultralytics.com/datasets/detect/african-wildlife/
# Example usage: yolo train data=african-wildlife.yaml
# parent
# ├── ultralytics
# └── datasets
#     └── african-wildlife  ← downloads here (100 MB)

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
path: ../datasets/african-wildlife # dataset root dir
train: train/images # train images (relative to 'path') 1052 images
val: valid/images # val images (relative to 'path') 225 images
test: test/images # test images (relative to 'path') 227 images

# Classes
names:
  0: buffalo
  1: elephant
  2: rhino
  3: zebra

# Download script/URL (optional)
download: https://github.com/ultralytics/assets/releases/download/v0.0.0/african-wildlife.zip
