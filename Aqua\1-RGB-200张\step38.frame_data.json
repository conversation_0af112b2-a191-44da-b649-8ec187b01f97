{"frame": 24560, "sequence": 0, "step": 38, "timestamp": 182.2958, "captures": [{"@type": "type.unity.com/unity.solo.RGBCamera", "id": "camera", "description": "", "position": [14175.66, -24.0102673, 45.97277], "rotation": [0.03323204, 0.966131151, 0.162227526, -0.197910368], "velocity": [0.0, 0.0, 0.0], "acceleration": [0.0, 0.0, 0.0], "filename": "step38.camera.png", "imageFormat": "Png", "dimension": [1920.0, 1080.0], "projection": "Perspective", "matrix": [1.73205054, 0.0, 0.0, 0.0, 3.079201, 0.0, 0.0, 0.0, -1.00000226], "annotations": [{"@type": "type.unity.com/unity.solo.BoundingBox2DAnnotation", "id": "bounding box", "sensorId": "camera", "description": "Produces 2D bounding box annotations for all visible objects that bear a label defined in this labeler's associated label configuration.", "values": [{"instanceId": 1, "labelId": 2, "labelName": "Observation Satellite - Aqua", "origin": [835.0, 289.0], "dimension": [250.0, 559.0]}]}]}]}