#!/usr/bin/env python3
"""
按照官方文档方式训练YOLOv13n Aqua卫星检测模型

基于官方README中的训练代码示例
"""

import os
import sys
import time
from pathlib import Path

def main():
    print("🚀 YOLOv13n Aqua卫星检测训练 (官方方式)")
    print("📊 数据划分: 7:2:1 (训练:验证:测试)")
    print("="*60)
    
    # 1. 检查前置条件
    print("🔍 检查前置条件...")
    
    # 检查数据集配置文件
    dataset_config = "datasets/aqua_yolo/aqua_dataset.yaml"
    if not os.path.exists(dataset_config):
        print(f"❌ 数据集配置文件不存在: {dataset_config}")
        print("请先运行: python convert_aqua_dataset.py")
        return
    
    # 检查YOLOv13n配置文件
    model_config = "yolov13n.yaml"
    if not os.path.exists(model_config):
        print(f"❌ YOLOv13n配置文件不存在: {model_config}")
        return
    
    print(f"✅ 数据集配置: {dataset_config}")
    print(f"✅ 模型配置: {model_config}")
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics导入成功")
        
        # 2. 创建模型 (按照官方文档)
        print(f"\n🔧 创建YOLOv13n模型...")
        model = YOLO('yolov13n.yaml')
        print("✅ YOLOv13n模型创建成功")
        
        # 3. 训练模型 (按照官方文档，但调整为适合小数据集的参数)
        print(f"\n🏋️ 开始训练...")
        
        start_time = time.time()
        
        # 训练参数 (基于官方示例，但适配Aqua数据集)
        results = model.train(
            data=dataset_config,        # 使用Aqua数据集
            epochs=100,                 # 减少轮数 (原600)
            batch=8,                    # 减少批次 (原256)
            imgsz=640,                  # 保持640
            scale=0.5,                  # nano规模
            mosaic=1.0,                 # 数据增强
            mixup=0.0,                  # nano不使用mixup
            copy_paste=0.1,             # 轻微copy_paste
            device="",                  # 自动选择设备
            project='runs/train',
            name='yolov13n_aqua_official',
            exist_ok=True,
            save=True,
            plots=True,
            val=True,
            verbose=True,
            # 额外的单类别优化参数
            single_cls=True,            # 单类别检测
            optimizer='AdamW',          # 优化器
            lr0=0.001,                  # 学习率
            lrf=0.01,                   # 最终学习率因子
            momentum=0.937,             # 动量
            weight_decay=0.0005,        # 权重衰减
            warmup_epochs=3.0,          # 预热轮数
            amp=True,                   # 混合精度
            patience=50,                # 早停耐心值
        )
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n✅ 训练完成!")
        print(f"⏱️  训练时间: {training_time/3600:.2f} 小时")
        
        # 4. 评估模型性能 (按照官方文档)
        print(f"\n📊 评估模型性能...")
        metrics = model.val(dataset_config)
        
        if metrics and hasattr(metrics, 'box'):
            print(f"📈 验证集结果:")
            print(f"   - mAP50-95: {metrics.box.map:.4f}")
            print(f"   - mAP50:    {metrics.box.map50:.4f}")
            print(f"   - mAP75:    {metrics.box.map75:.4f}")
            print(f"   - 精确度:   {metrics.box.mp:.4f}")
            print(f"   - 召回率:   {metrics.box.mr:.4f}")
        
        # 5. 保存最佳模型
        best_model_path = "runs/train/yolov13n_aqua_official/weights/best.pt"
        if os.path.exists(best_model_path):
            import shutil
            shutil.copy2(best_model_path, "yolov13n_aqua_official.pt")
            print(f"\n💾 最佳模型已保存: yolov13n_aqua_official.pt")
        
        # 6. 测试集评估
        print(f"\n🧪 测试集评估...")
        try:
            test_metrics = model.val(data=dataset_config, split='test')
            if test_metrics and hasattr(test_metrics, 'box'):
                print(f"📊 测试集结果:")
                print(f"   - mAP50-95: {test_metrics.box.map:.4f}")
                print(f"   - mAP50:    {test_metrics.box.map50:.4f}")
                print(f"   - mAP75:    {test_metrics.box.map75:.4f}")
                print(f"   - 精确度:   {test_metrics.box.mp:.4f}")
                print(f"   - 召回率:   {test_metrics.box.mr:.4f}")
        except Exception as e:
            print(f"⚠️  测试集评估失败: {e}")
        
        # 7. 示例推理 (按照官方文档)
        print(f"\n🔍 示例推理...")
        test_images_dir = Path("datasets/aqua_yolo/images/test")
        if test_images_dir.exists():
            test_images = list(test_images_dir.glob("*.jpg"))
            if test_images:
                sample_image = test_images[0]
                print(f"📸 测试图片: {sample_image}")
                
                # 执行推理
                results = model(str(sample_image))
                
                # 显示结果
                if results and len(results) > 0:
                    detections = len(results[0].boxes) if results[0].boxes is not None else 0
                    print(f"✅ 检测到 {detections} 个Aqua卫星")
                    
                    # 保存结果图片
                    results[0].save(filename="aqua_detection_result.jpg")
                    print(f"💾 检测结果已保存: aqua_detection_result.jpg")
        
        print(f"\n🎉 训练和评估完成!")
        print(f"📁 训练结果: runs/train/yolov13n_aqua_official/")
        print(f"🎯 使用模型:")
        print(f"   from ultralytics import YOLO")
        print(f"   model = YOLO('yolov13n_aqua_official.pt')")
        print(f"   results = model('your_image.jpg')")
        
    except ImportError as e:
        print(f"❌ ultralytics导入失败: {e}")
        print("请确保已安装ultralytics: pip install ultralytics")
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
