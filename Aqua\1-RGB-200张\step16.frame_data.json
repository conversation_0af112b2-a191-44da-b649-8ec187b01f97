{"frame": 10865, "sequence": 0, "step": 16, "timestamp": 82.99885, "captures": [{"@type": "type.unity.com/unity.solo.RGBCamera", "id": "camera", "description": "", "position": [14202.9277, 7.006492e-45, -17.2568569], "rotation": [0.0471139178, -0.5712716, 0.03287317, 0.8187481], "velocity": [0.0, 0.0, 0.0], "acceleration": [0.0, 0.0, 0.0], "filename": "step16.camera.png", "imageFormat": "Png", "dimension": [1920.0, 1080.0], "projection": "Perspective", "matrix": [1.73205054, 0.0, 0.0, 0.0, 3.079201, 0.0, 0.0, 0.0, -1.00000226], "annotations": [{"@type": "type.unity.com/unity.solo.BoundingBox2DAnnotation", "id": "bounding box", "sensorId": "camera", "description": "Produces 2D bounding box annotations for all visible objects that bear a label defined in this labeler's associated label configuration.", "values": [{"instanceId": 1, "labelId": 2, "labelName": "Observation Satellite - Aqua", "origin": [816.0, 299.0], "dimension": [231.0, 606.0]}]}]}]}