#!/usr/bin/env python3
"""
YOLOv13 基础测试脚本

先测试模型的基本加载和推理功能，然后再尝试验证
"""

import os
import sys

def test_model_loading():
    """测试模型加载"""
    print("🔧 测试模型加载...")
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics导入成功")
        
        # 测试加载YOLOv13n
        if os.path.exists('yolov13n.pt'):
            print("📦 加载 yolov13n.pt...")
            model = YOLO('yolov13n.pt')
            print("✅ yolov13n.pt 加载成功")
            
            # 显示模型信息
            print(f"📊 模型信息:")
            print(f"   - 类型: {type(model)}")
            if hasattr(model, 'model'):
                print(f"   - 内部模型: {type(model.model)}")
            
            return model
        else:
            print("❌ 未找到 yolov13n.pt")
            return None
            
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_simple_inference(model):
    """测试简单推理"""
    print("\n🔍 测试简单推理...")
    
    try:
        # 检查是否有测试图片
        test_images = [
            'ultralytics/assets/bus.jpg',
            'ultralytics/assets/zidane.jpg'
        ]
        
        test_image = None
        for img in test_images:
            if os.path.exists(img):
                test_image = img
                break
        
        if test_image:
            print(f"📸 使用测试图片: {test_image}")
            results = model.predict(source=test_image, verbose=False)
            print("✅ 推理成功")
            
            if results and len(results) > 0:
                result = results[0]
                if result.boxes is not None:
                    print(f"   - 检测到 {len(result.boxes)} 个目标")
                    
                    # 显示检测结果
                    for i, box in enumerate(result.boxes):
                        if i < 3:  # 只显示前3个
                            conf = float(box.conf[0])
                            cls = int(box.cls[0])
                            print(f"   - 目标{i+1}: 类别{cls}, 置信度{conf:.3f}")
                else:
                    print("   - 未检测到目标")
            
            return True
        else:
            print("❌ 未找到测试图片")
            return False
            
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_simple(model):
    """测试简单验证"""
    print("\n🧪 测试简单验证...")
    
    try:
        # 先尝试使用coco8（小数据集）
        print("尝试使用 coco8.yaml (小数据集)...")
        metrics = model.val(data='coco8.yaml', verbose=True)
        print("✅ coco8 验证成功")
        
        if hasattr(metrics, 'box'):
            print(f"📊 验证结果:")
            print(f"   - mAP50-95: {metrics.box.map:.4f}")
            print(f"   - mAP50:    {metrics.box.map50:.4f}")
            print(f"   - mAP75:    {metrics.box.map75:.4f}")
        
        return metrics
        
    except Exception as e:
        print(f"❌ coco8 验证失败: {e}")
        
        # 如果coco8失败，尝试完整的coco
        try:
            print("尝试使用 coco.yaml (完整数据集)...")
            metrics = model.val(data='coco.yaml', batch=1, verbose=True)
            print("✅ coco 验证成功")
            
            if hasattr(metrics, 'box'):
                print(f"📊 验证结果:")
                print(f"   - mAP50-95: {metrics.box.map:.4f}")
                print(f"   - mAP50:    {metrics.box.map50:.4f}")
                print(f"   - mAP75:    {metrics.box.map75:.4f}")
            
            return metrics
            
        except Exception as e2:
            print(f"❌ coco 验证也失败: {e2}")
            return None

def main():
    print("🚀 YOLOv13 基础功能测试")
    print("="*50)
    
    # 1. 测试模型加载
    model = test_model_loading()
    if model is None:
        print("❌ 模型加载失败，测试终止")
        return
    
    # 2. 测试简单推理
    inference_ok = test_simple_inference(model)
    if not inference_ok:
        print("⚠️  推理测试失败，但继续尝试验证")
    
    # 3. 测试验证
    metrics = test_validation_simple(model)
    
    if metrics:
        print("\n✅ 所有测试完成!")
        print("模型工作正常，可以进行完整的COCO验证")
    else:
        print("\n❌ 验证测试失败")
        print("可能的原因:")
        print("1. 网络连接问题，无法下载数据集")
        print("2. 磁盘空间不足")
        print("3. 数据集配置问题")
        print("4. 模型兼容性问题")

if __name__ == '__main__':
    main()
