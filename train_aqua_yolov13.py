#!/usr/bin/env python3
"""
YOLOv13 Aqua卫星数据集训练脚本

使用YOLOv13训练Aqua卫星检测模型
支持不同规模的模型和自定义训练参数
"""

import os
import sys
import argparse
import time
from pathlib import Path

def train_yolov13n_aqua(epochs=100, batch_size=16, imgsz=640, device=''):
    """
    训练YOLOv13n Aqua卫星检测模型

    Args:
        epochs: 训练轮数
        batch_size: 批次大小
        imgsz: 输入图像尺寸
        device: 设备 ('', '0', 'cpu')
    """
    print("🚀 YOLOv13n Aqua卫星检测模型训练")
    print("="*60)
    
    try:
        from ultralytics import YOLO
        print("✅ ultralytics导入成功")
    except ImportError as e:
        print(f"❌ ultralytics导入失败: {e}")
        print("请确保已安装ultralytics: pip install ultralytics")
        return None
    
    # 检查数据集配置文件
    dataset_config = "datasets/aqua_yolo/aqua_dataset.yaml"
    if not os.path.exists(dataset_config):
        print(f"❌ 数据集配置文件不存在: {dataset_config}")
        print("请先运行: python convert_aqua_dataset.py")
        return None
    
    # 使用YOLOv13n模型
    model_config = "ultralytics/cfg/models/v13/yolov13.yaml"

    if not os.path.exists(model_config):
        print(f"❌ YOLOv13配置文件不存在: {model_config}")
        return None

    print(f"📊 训练配置:")
    print(f"   - 模型: YOLOv13n (Nano)")
    print(f"   - 配置文件: {model_config}")
    print(f"   - 数据集: {dataset_config}")
    print(f"   - 数据划分: 7:2:1 (训练:验证:测试)")
    print(f"   - 训练轮数: {epochs}")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 图像尺寸: {imgsz}")
    print(f"   - 设备: {device if device else 'auto'}")
    
    try:
        # 创建YOLOv13n模型
        print(f"\n🔧 创建YOLOv13n模型...")

        # 尝试不同的模型创建方式
        try:
            # 方法1: 直接使用yolov13n
            model = YOLO('yolov13n.yaml')
            print("✅ 使用yolov13n.yaml创建模型成功")
        except:
            try:
                # 方法2: 使用统一配置文件
                model = YOLO(model_config)
                print("✅ 使用yolov13.yaml创建模型成功")
            except Exception as e:
                print(f"❌ 模型创建失败: {e}")
                return None
        
        # 开始训练
        print(f"\n🏋️ 开始训练...")
        start_time = time.time()
        
        # 训练参数
        train_args = {
            'data': dataset_config,
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': imgsz,
            'device': device,
            'project': 'runs/train',
            'name': 'yolov13n_aqua',
            'exist_ok': True,
            'pretrained': True,  # 使用预训练权重
            'optimizer': 'AdamW',
            'lr0': 0.001,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'pose': 12.0,
            'kobj': 1.0,
            'label_smoothing': 0.0,
            'nbs': 64,
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 0.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.0,
            'copy_paste': 0.0,
            'auto_augment': 'randaugment',
            'erasing': 0.4,
            'crop_fraction': 1.0,
            'save': True,
            'save_period': 10,
            'cache': False,
            'device': device,
            'workers': 8,
            'project': 'runs/train',
            'name': 'yolov13n_aqua',
            'exist_ok': True,
            'pretrained': True,
            'verbose': True,
            'seed': 0,
            'deterministic': True,
            'single_cls': True,  # 单类别检测
            'rect': False,
            'cos_lr': False,
            'close_mosaic': 10,
            'resume': False,
            'amp': True,
            'fraction': 1.0,
            'profile': False,
            'freeze': None,
            'multi_scale': False,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'val': True,
            'split': 'val',
            'plots': True
        }
        
        # 执行训练
        results = model.train(**train_args)
        
        end_time = time.time()
        training_time = end_time - start_time
        
        print(f"\n🎉 训练完成!")
        print(f"⏱️  训练时间: {training_time/3600:.2f} 小时")
        
        # 显示训练结果
        if results:
            print(f"\n📊 训练结果:")
            try:
                print(f"   - 最佳mAP50-95: {results.box.map:.4f}")
                print(f"   - 最佳mAP50: {results.box.map50:.4f}")
                print(f"   - 最佳mAP75: {results.box.map75:.4f}")
            except:
                print(f"   - 训练完成，详细结果请查看runs目录")
        
        # 验证模型
        print(f"\n🔍 验证训练后的模型...")
        val_results = model.val()
        
        if val_results:
            print(f"📈 验证结果:")
            print(f"   - mAP50-95: {val_results.box.map:.4f}")
            print(f"   - mAP50: {val_results.box.map50:.4f}")
            print(f"   - mAP75: {val_results.box.map75:.4f}")
            print(f"   - 精确度: {val_results.box.mp:.4f}")
            print(f"   - 召回率: {val_results.box.mr:.4f}")
        
        # 保存最终模型
        model_save_path = "yolov13n_aqua_best.pt"
        best_model_path = "runs/train/yolov13n_aqua/weights/best.pt"

        if os.path.exists(best_model_path):
            import shutil
            shutil.copy2(best_model_path, model_save_path)
            print(f"\n💾 最佳模型已保存: {model_save_path}")

        print(f"\n✅ YOLOv13n训练流程完成!")
        print(f"📁 训练结果目录: runs/train/yolov13n_aqua")
        print(f"🎯 下一步:")
        print(f"   1. 查看训练图表: runs/train/yolov13n_aqua/")
        print(f"   2. 测试模型: python test_aqua_model.py --model yolov13n_aqua_best.pt")
        print(f"   3. 在测试集上评估: python test_aqua_model.py --source datasets/aqua_yolo/images/test")
        print(f"   4. 导出模型: model.export(format='onnx')")
        
        return model
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    parser = argparse.ArgumentParser(description='YOLOv13n Aqua卫星检测训练')
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--batch', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='输入图像尺寸')
    parser.add_argument('--device', type=str, default='',
                       help='设备 (空=auto, 0=GPU0, cpu=CPU)')

    args = parser.parse_args()

    # 开始训练YOLOv13n
    model = train_yolov13n_aqua(
        epochs=args.epochs,
        batch_size=args.batch,
        imgsz=args.imgsz,
        device=args.device
    )
    
    if model is None:
        print("❌ 训练失败")
        sys.exit(1)
    else:
        print("🎉 训练成功完成!")

if __name__ == '__main__':
    main()
